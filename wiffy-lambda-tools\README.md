# WIFY Lambda Tools

A collection of bash scripts for AWS Lambda function management, including cloning and downloading Lambda functions.

## Scripts Overview

### 1. `clonelambda.sh` - Complete Lambda Function Cloning
A comprehensive script that creates an exact copy of an AWS Lambda function, preserving all configurations and settings.

### 2. `downloadlambda.sh` - Bulk Lambda Code Download
A script for downloading deployment packages from multiple Lambda functions in batch.

## Prerequisites

- **AWS CLI** - Configured with appropriate credentials and permissions
- **jq** - JSON processor for parsing AWS API responses
- **curl** - For downloading deployment packages
- **bash** - Version 3.2+ (macOS compatible)

### Installation of Dependencies

```bash
# Install AWS CLI
curl "https://awscli.amazonaws.com/AWSCLIV2.pkg" -o "AWSCLIV2.pkg"
sudo installer -pkg AWSCLIV2.pkg -target /

# Install jq (macOS with Homebrew)
brew install jq

# Install jq (Linux)
sudo apt-get install jq  # Ubuntu/Debian
sudo yum install jq      # CentOS/RHEL
```

## Usage

### Clone Lambda Function

```bash
./clonelambda.sh <source-function-name> <target-function-name> [region]
```

**Parameters:**
- `source-function-name`: Name of the existing Lambda function to clone
- `target-function-name`: Name for the new cloned function
- `region`: AWS region (optional, uses `AWS_DEFAULT_REGION` if not specified)

**Example:**
```bash
./clonelambda.sh my-original-function my-cloned-function us-east-1
```

**What gets cloned:**
- ✅ Function code (both Zip and Container image packages)
- ✅ Runtime configuration (memory, timeout, handler)
- ✅ Environment variables
- ✅ IAM role and permissions
- ✅ VPC configuration
- ✅ Layers
- ✅ KMS encryption settings
- ✅ Tracing configuration
- ✅ Architecture settings (x86_64/arm64)
- ✅ Ephemeral storage configuration
- ✅ EFS file system configurations
- ✅ Tags
- ✅ Reserved concurrency settings
- ✅ Asynchronous invocation configuration
- ❌ Event source mappings/triggers (intentionally excluded to prevent duplicate processing)

### Download Lambda Code

```bash
./downloadlambda.sh
```

This script downloads deployment packages for a predefined list of Lambda functions. The function names are hardcoded in the script and will need to be modified for your use case.

**Output:** Creates `.zip` files named after each Lambda function in the current directory.

## Configuration

### Setting AWS Region

You can specify the AWS region in several ways:

1. **Command line parameter** (highest priority):
   ```bash
   ./clonelambda.sh source-func target-func us-west-2
   ```

2. **Environment variable**:
   ```bash
   export AWS_DEFAULT_REGION=us-west-2
   ./clonelambda.sh source-func target-func
   ```

3. **AWS CLI configuration**:
   ```bash
   aws configure set region us-west-2
   ```

### Customizing Download List

To modify the list of functions in `downloadlambda.sh`, edit the `lambdas` array:

```bash
lambdas=(
    "your-function-1"
    "your-function-2"
    "your-function-3"
)
```

## Required AWS Permissions

The scripts require the following AWS IAM permissions:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "lambda:GetFunction",
                "lambda:GetFunctionConfiguration",
                "lambda:CreateFunction",
                "lambda:ListTags",
                "lambda:GetFunctionConcurrency",
                "lambda:PutFunctionConcurrency",
                "lambda:GetFunctionEventInvokeConfig",
                "lambda:PutFunctionEventInvokeConfig"
            ],
            "Resource": "*"
        }
    ]
}
```

## Error Handling

Both scripts include robust error handling:

- **Dependency checks**: Verifies that `aws`, `jq`, and `curl` are installed
- **Parameter validation**: Ensures required parameters are provided
- **Safe execution**: Uses `set -euo pipefail` for strict error handling
- **Cleanup**: Automatic cleanup of temporary files

## Limitations

1. **Event triggers**: The clone script intentionally does not copy event source mappings to prevent duplicate event processing
2. **Cross-account cloning**: Requires appropriate cross-account IAM permissions
3. **Large deployments**: Very large deployment packages may take time to download and upload

## Troubleshooting

### Common Issues

1. **"aws not found"**: Install and configure AWS CLI
2. **"jq not found"**: Install jq JSON processor
3. **"region not set"**: Set AWS region via parameter or environment variable
4. **Permission denied**: Ensure your AWS credentials have the required Lambda permissions

### Debug Mode

For troubleshooting, you can enable debug output:

```bash
bash -x ./clonelambda.sh source-func target-func us-east-1
```

## Contributing

Feel free to submit issues and enhancement requests!

## License

This project is open source. Please check with your organization's policies before using in production environments.
