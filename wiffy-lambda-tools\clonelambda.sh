#!/usr/bin/env bash
# Null-safe clone of a Lambda (works on macOS bash 3.2/zsh). No mapfile.
set -euo pipefail

SRC_FUNC="${1:-}"
DST_FUNC="${2:-}"
REGION="${3:-${AWS_DEFAULT_REGION:-}}"

if [[ -z "$SRC_FUNC" || -z "$DST_FUNC" ]]; then
  echo "Usage: $0 <source-function-name> <target-function-name> [region]"
  exit 1
fi
if [[ -z "${REGION}" ]]; then
  echo "Error: region not set. Pass as 3rd arg or set AWS_DEFAULT_REGION."
  exit 1
fi

for bin in aws jq curl; do command -v "$bin" >/dev/null || { echo "$bin not found"; exit 1; }; done

echo "Cloning Lambda '${SRC_FUNC}' -> '${DST_FUNC}' in region ${REGION}"

CFG_JSON="$(aws lambda get-function-configuration --function-name "$SRC_FUNC" --region "$REGION")"
CODE_JSON="$(aws lambda get-function --function-name "$SRC_FUNC" --region "$REGION")"

# Safe extractors (avoid iterating nulls)
PKG_TYPE="$(echo "$CFG_JSON" | jq -r '.PackageType // "Zip"')"
SIGNED_URL="$(echo "$CODE_JSON" | jq -r '.Code.Location // empty')"
IMAGE_URI="$(echo "$CFG_JSON" | jq -r '.Code.ImageUri // empty')"

ROLE_ARN="$(echo "$CFG_JSON" | jq -r '.Role')"
RUNTIME="$(echo "$CFG_JSON" | jq -r '.Runtime // empty')"
HANDLER="$(echo "$CFG_JSON" | jq -r '.Handler // empty')"
TIMEOUT="$(echo "$CFG_JSON" | jq -r '.Timeout // 3')"
MEMORY="$(echo "$CFG_JSON" | jq -r '.MemorySize // 128')"
DESC="$(echo "$CFG_JSON" | jq -r '.Description // ""')"
KMS_KEY="$(echo "$CFG_JSON" | jq -r '.KMSKeyArn // empty')"
TRACE_MODE="$(echo "$CFG_JSON" | jq -r '.TracingConfig.Mode // "PassThrough"')"
ARCHS="$(echo "$CFG_JSON" | jq -c '.Architectures // ["x86_64"]')"
EPHEM="$(echo "$CFG_JSON" | jq -r '.EphemeralStorage.Size // empty')"

# ENV (object or empty)
ENV_JSON="$(echo "$CFG_JSON" | jq -c '(.Environment // {}) | (.Variables // {})')"

# Layers (array of ARNs or empty) — guard evaluation first
LAYERS_ARR="$(echo "$CFG_JSON" | jq -c 'if (.Layers? | type=="array") then [.Layers[]? .Arn] else [] end')"

# VPC pieces — coalesce parent first, then index
SUBNETS="$(echo "$CFG_JSON" | jq -c '((.VpcConfig // {}) | .SubnetIds // [])')"
SGS="$(echo "$CFG_JSON" | jq -c '((.VpcConfig // {}) | .SecurityGroupIds // [])')"

# EFS
FSCONF="$(echo "$CFG_JSON" | jq -c '(.FileSystemConfigs // [])')"

# Image config (object or {})
IMGCFG="$(echo "$CFG_JSON" | jq -c '(.ImageConfig // {})')"

# Tags (object or {})
TAGS_JSON="$(aws lambda list-tags --resource "$(echo "$CODE_JSON" | jq -r '.Configuration.FunctionArn')" --region "$REGION" | jq -c '.Tags // {}')"

# Reserved concurrency & async config (may not exist)
set +e
CONC_VAL="$(aws lambda get-function-concurrency --function-name "$SRC_FUNC" --region "$REGION" 2>/dev/null | jq -r '.ReservedConcurrentExecutions // empty')"
ASYNC_JSON="$(aws lambda get-function-event-invoke-config --function-name "$SRC_FUNC" --region "$REGION" 2>/dev/null | jq -c '.')"
set -e

TMPDIR="$(mktemp -d)"; ZIP_PATH="${TMPDIR}/code.zip"
cleanup(){ rm -rf "$TMPDIR"; }
trap cleanup EXIT

CREATE_ARGS=( --function-name "$DST_FUNC" --role "$ROLE_ARN" --region "$REGION" )

# architectures
if [[ "$ARCHS" != "[]" ]]; then
  ARCH_ARGS=""
  for arch in $(echo "$ARCHS" | jq -r '.[]'); do
    ARCH_ARGS="$ARCH_ARGS $arch"
  done
  # shellcheck disable=SC2206
  CREATE_ARGS+=( --architectures ${ARCH_ARGS} )
fi

# tracing
CREATE_ARGS+=( --tracing-config "Mode=$TRACE_MODE" )

# env
if [[ "$ENV_JSON" != "{}" ]]; then
  CREATE_ARGS+=( --environment "Variables=$(echo "$ENV_JSON" | jq -c '.')" )
fi

# layers
if [[ "$LAYERS_ARR" != "[]" ]]; then
  LAYER_ARGS=""
  for layer in $(echo "$LAYERS_ARR" | jq -r '.[]'); do
    LAYER_ARGS="$LAYER_ARGS $layer"
  done
  # shellcheck disable=SC2206
  CREATE_ARGS+=( --layers ${LAYER_ARGS} )
fi

# description
if [[ -n "$DESC" ]]; then
  CREATE_ARGS+=( --description "$DESC" )
fi

# kms
if [[ -n "$KMS_KEY" && "$KMS_KEY" != "null" ]]; then
  CREATE_ARGS+=( --kms-key-arn "$KMS_KEY" )
fi

# ephemeral storage
if [[ -n "$EPHEM" && "$EPHEM" != "null" ]]; then
  CREATE_ARGS+=( --ephemeral-storage "Size=$EPHEM" )
fi

# VPC
if [[ "$(echo "$SUBNETS" | jq 'length')" -gt 0 ]]; then
  SUBNET_LIST="$(echo "$SUBNETS" | jq -r '.[]' | paste -sd, -)"
  SG_LIST="$(echo "$SGS" | jq -r '.[]' | paste -sd, -)"
  CREATE_ARGS+=( --vpc-config "SubnetIds=$SUBNET_LIST,SecurityGroupIds=$SG_LIST" )
fi

# EFS
if [[ "$(echo "$FSCONF" | jq 'length')" -gt 0 ]]; then
  CREATE_ARGS+=( --file-system-configs "$(echo "$FSCONF" | jq -c '.')" )
fi

# Tags
if [[ "$TAGS_JSON" != "{}" ]]; then
  TAGS_KV="$(echo "$TAGS_JSON" | jq -r 'to_entries | map("\(.key)=\(.value)") | join(" ")')"
  CREATE_ARGS+=( --tags "$TAGS_KV" )
fi

if [[ "$PKG_TYPE" == "Image" ]]; then
  echo "Detected package type: Image"
  if [[ -z "$IMAGE_URI" || "$IMAGE_URI" == "null" ]]; then
    echo "Error: Image package but no ImageUri found."
    exit 1
  fi
  CREATE_ARGS+=( --package-type Image --code "ImageUri=$IMAGE_URI" )

  # optional image config
  if [[ "$IMGCFG" != "{}" ]]; then
    ENTRY_POINT="$(echo "$IMGCFG" | jq -c '.EntryPoint // empty')"
    CMD="$(echo "$IMGCFG" | jq -c '.Command // empty')"
    WDIR="$(echo "$IMGCFG" | jq -r '.WorkingDirectory // empty')"
    IC_JSON=$(jq -n --argjson ep "${ENTRY_POINT:-null}" --argjson cmd "${CMD:-null}" --arg wd "${WDIR:-null}" '
      {
        EntryPoint: ($ep // empty),
        Command: ($cmd // empty),
        WorkingDirectory: ($wd // empty)
      } | with_entries(select(.value != null and .value != ""))')
    if [[ "$(echo "$IC_JSON" | jq 'length')" -gt 0 ]]; then
      CREATE_ARGS+=( --image-config "$IC_JSON" )
    fi
  fi

  echo "Creating new function (Image)..."
  aws lambda create-function "${CREATE_ARGS[@]}" >/dev/null
else
  echo "Detected package type: Zip"
  CREATE_ARGS+=( --runtime "$RUNTIME" --handler "$HANDLER" --memory-size "$MEMORY" --timeout "$TIMEOUT" )
  echo "Downloading deployment package..."
  curl -sSL -o "$ZIP_PATH" "$SIGNED_URL"
  echo "Creating new function (Zip)..."
  aws lambda create-function "${CREATE_ARGS[@]}" --zip-file "fileb://$ZIP_PATH" >/dev/null
fi

echo "Function created: $DST_FUNC"

# Reserved concurrency
if [[ -n "${CONC_VAL:-}" ]]; then
  echo "Setting reserved concurrency: $CONC_VAL"
  aws lambda put-function-concurrency \
    --function-name "$DST_FUNC" \
    --reserved-concurrent-executions "$CONC_VAL" \
    --region "$REGION" >/dev/null
fi

# Async (event-invoke) config
if [[ -n "${ASYNC_JSON:-}" && "$ASYNC_JSON" != "null" ]]; then
  echo "Cloning async (event-invoke) config"
  DEST=$(echo "$ASYNC_JSON" | jq -c '.DestinationConfig // {}')
  MAXRETRY=$(echo "$ASYNC_JSON" | jq -r '.MaximumRetryAttempts // empty')
  MAXAGE=$(echo "$ASYNC_JSON" | jq -r '.MaximumEventAgeInSeconds // empty')

  ARGS=( --function-name "$DST_FUNC" )
  if [[ "$DEST" != "{}" ]]; then
    ARGS+=( --destination-config "$DEST" )
  fi
  if [[ -n "$MAXRETRY" && "$MAXRETRY" != "null" ]]; then
    ARGS+=( --maximum-retry-attempts "$MAXRETRY" )
  fi
  if [[ -n "$MAXAGE" && "$MAXAGE" != "null" ]]; then
    ARGS+=( --maximum-event-age-in-seconds "$MAXAGE" )
  fi
  aws lambda put-function-event-invoke-config "${ARGS[@]}" --region "$REGION" >/dev/null
fi

# Triggers not cloned by default to avoid double-processing.
echo "✅ Clone complete: ${DST_FUNC}"

