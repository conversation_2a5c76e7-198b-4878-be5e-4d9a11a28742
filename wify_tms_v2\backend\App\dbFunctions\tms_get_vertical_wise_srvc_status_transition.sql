CREATE OR REPLACE FUNCTION public.tms_get_vertical_wise_srvc_status_transition(vertical_id_ integer, materialized_table_name_ text, assgn_to_prvdr_start_date_ timestamp, assgn_to_prvdr_end_date_ timestamp)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
-- Declarations
declare
	_dynamic_columns text;
	_single_status_title text;
	_single_status_extracter text;
	all_status_titles text[];
	_dynamic_sql text;

	begin

		-- Get all unique status titles for the vertical's service types
		-- Group by title to handle cases where multiple status_keys have the same title
		SELECT array_agg(DISTINCT srvc_statuses.title ORDER BY srvc_statuses.title)
		  FROM cl_tx_orgs_settings as vertical_settings
		 INNER JOIN cl_cf_srvc_statuses as srvc_statuses 
		    ON srvc_statuses.srvc_id = ANY(array(SELECT json_array_elements_text(vertical_settings.settings_data->'srvc_type_id'))::integer[])
		 WHERE vertical_settings.db_id = vertical_id_
		  INTO all_status_titles;

		-- Build dynamic columns for each unique status title
		_dynamic_columns = '';
		
		FOREACH _single_status_title IN ARRAY all_status_titles
		LOOP
			_single_status_extracter = '( 
											select trnstn_log.trnstn_date
											  from cl_tx_srvc_req_trnstn_log as trnstn_log 
											 inner join cl_cf_srvc_statuses as status_ref
											    on status_ref.status_key = trnstn_log.status_key
											   and status_ref.srvc_id = srvc_req.srvc_type_id
										     where trnstn_log.srvc_req_id = srvc_req.db_id
										       and status_ref.title = $$'|| _single_status_title || '$$ 
										     order by trnstn_log.trnstn_date desc
										     limit 1
										  )';
			
			_dynamic_columns = _dynamic_columns || ', ' || _single_status_extracter  || ' as "' ||  _single_status_title || '" '; 
		   
	    END LOOP;

		-- Get vertical name and create materialized view
		_dynamic_sql = 'CREATE MATERIALIZED VIEW '||materialized_table_name_||' AS
								select
									vertical_settings.settings_data->>$$vertical_title$$ as "Vertical Name",
								   srvc_req.display_code as "Service Display Code",
								   org.nickname as "Org Name",
								   srvc_type.title as "Service Type"
								   ' || _dynamic_columns || '
							      from public.cl_tx_srvc_req as srvc_req
							     inner join cl_tx_orgs_settings as vertical_settings
							        on vertical_settings.db_id = ' || vertical_id_ || '
							     inner join cl_tx_orgs as org
							        on org.org_id = srvc_req.org_id
							     inner join cl_cf_service_types as srvc_type
							        on srvc_type.service_type_id = srvc_req.srvc_type_id
								 where srvc_req.prvdr_vertical = ' || vertical_id_ || '
								   and srvc_req.srvc_prvdr_assg_time >= $$' || assgn_to_prvdr_start_date_ || '$$
								   and srvc_req.srvc_prvdr_assg_time <= $$' || assgn_to_prvdr_end_date_ || '$$
								 order by srvc_req.db_id desc
					';

		raise notice 'vertical status transition sql %', _dynamic_sql;

		execute _dynamic_sql;
		return '{}';

	END;
$function$
;
